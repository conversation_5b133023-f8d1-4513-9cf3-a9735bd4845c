@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 142 76% 56%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 142 76% 56%;
    --chart-2: 142 76% 46%;
    --chart-3: 142 76% 36%;
    --chart-4: 142 76% 66%;
    --chart-5: 142 76% 76%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  /* Glassmorphism utilities */
  .glass {
    @apply bg-white/20 backdrop-blur-md border border-white/20;
  }

  .glass-dark {
    @apply bg-black/20 backdrop-blur-md border border-white/10;
  }

  /* Consistent card styles */
  .card-glass {
    @apply bg-white/80 backdrop-blur-sm border border-white/50 rounded-2xl sm:rounded-3xl shadow-soft hover:bg-white/90 transition-all duration-300;
  }

  .card-glass-mobile {
    @apply bg-white/80 backdrop-blur-sm border border-white/50 rounded-xl sm:rounded-2xl shadow-soft;
  }

  /* Modern shadows */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }

  .shadow-glow-emerald {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.15), 0 0 40px rgba(16, 185, 129, 0.1);
  }

  .shadow-glow-green {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.15), 0 0 40px rgba(34, 197, 94, 0.1);
  }

  .shadow-glow-blue {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.15), 0 0 40px rgba(59, 130, 246, 0.1);
  }

  /* Enhanced shadows for desktop */
  .shadow-elevated {
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 30px -5px rgba(0, 0, 0, 0.04);
  }

  /* Force remove modal shadows */
  [data-radix-dialog-content] {
    box-shadow: none !important;
    border: none !important;
  }

  /* Gradient backgrounds */
  .bg-gradient-emerald {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  }

  .bg-gradient-green-soft {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 50%, #a7f3d0 100%);
  }

  .bg-gradient-mesh {
    background:
      radial-gradient(at 40% 20%, hsla(142, 76%, 76%, 0.3) 0px, transparent 50%),
      radial-gradient(at 80% 0%, hsla(142, 76%, 66%, 0.2) 0px, transparent 50%),
      radial-gradient(at 0% 50%, hsla(142, 76%, 56%, 0.2) 0px, transparent 50%),
      radial-gradient(at 80% 50%, hsla(142, 76%, 46%, 0.1) 0px, transparent 50%),
      radial-gradient(at 0% 100%, hsla(142, 76%, 36%, 0.2) 0px, transparent 50%),
      radial-gradient(at 80% 100%, hsla(142, 76%, 86%, 0.3) 0px, transparent 50%),
      radial-gradient(at 0% 0%, hsla(142, 76%, 96%, 0.4) 0px, transparent 50%);
  }

  /* Animation utilities */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-soft {
    animation: pulse-soft 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Smooth transitions */
  .transition-all-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-soft {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}
